import { v } from 'convex/values';
import { mutation, query } from './_generated/server';

// Create a new category
export const createCategory = mutation({
  args: {
    name: v.string(),
    ageUnder: v.optional(v.number()),
    ageAbove: v.optional(v.number()),
    gender: v.optional(
      v.union(v.literal('male'), v.literal('female'), v.literal('open')),
    ),
    isPwd: v.boolean(),
  },
  handler: async (ctx, args) => {
    const categoryId = await ctx.db.insert('categories', {
      name: args.name,
      ageUnder: args.ageUnder,
      ageAbove: args.ageAbove,
      gender: args.gender,
      isPwd: args.isPwd,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return categoryId;
  },
});

// Get all categories
export const getCategories = query({
  handler: async (ctx) => {
    return await ctx.db.query('categories').collect();
  },
});

// Get category by ID
export const getCategoryById = query({
  args: { categoryId: v.id('categories') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.categoryId);
  },
});

// Update category
export const updateCategory = mutation({
  args: {
    categoryId: v.id('categories'),
    name: v.optional(v.string()),
    ageUnder: v.optional(v.number()),
    ageAbove: v.optional(v.number()),
    gender: v.optional(
      v.union(v.literal('male'), v.literal('female'), v.literal('open')),
    ),
    isPwd: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { categoryId, ...updates } = args;

    // Filter out undefined values
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined),
    );

    if (Object.keys(filteredUpdates).length > 0) {
      await ctx.db.patch(categoryId, {
        ...filteredUpdates,
        updatedAt: Date.now(),
      });
    }

    return categoryId;
  },
});

// Delete category
export const deleteCategory = mutation({
  args: { categoryId: v.id('categories') },
  handler: async (ctx, args) => {
    // Check if category is being used in any tournaments
    const tournamentCategories = await ctx.db
      .query('tournament_categories')
      .withIndex('by_category', (q) => q.eq('categoryId', args.categoryId))
      .collect();

    if (tournamentCategories.length > 0) {
      throw new Error(
        'Cannot delete category that is being used in tournaments',
      );
    }

    await ctx.db.delete(args.categoryId);
    return args.categoryId;
  },
});

// Seed default categories
export const seedDefaultCategories = mutation({
  handler: async (ctx) => {
    // Check if categories already exist
    const existingCategories = await ctx.db.query('categories').collect();
    if (existingCategories.length > 0) {
      return existingCategories.map((cat) => cat._id);
    }

    const defaultCategories = [
      { name: 'Open', gender: 'open' as const, isPwd: false },
      { name: 'Women', gender: 'female' as const, isPwd: false },
      { name: 'Under 8', ageUnder: 8, gender: 'open' as const, isPwd: false },
      { name: 'Under 10', ageUnder: 10, gender: 'open' as const, isPwd: false },
      { name: 'Under 12', ageUnder: 12, gender: 'open' as const, isPwd: false },
      { name: 'Under 14', ageUnder: 14, gender: 'open' as const, isPwd: false },
      { name: 'Under 16', ageUnder: 16, gender: 'open' as const, isPwd: false },
      { name: 'Under 18', ageUnder: 18, gender: 'open' as const, isPwd: false },
      { name: 'Under 20', ageUnder: 20, gender: 'open' as const, isPwd: false },
      {
        name: 'Senior (50+)',
        ageAbove: 50,
        gender: 'open' as const,
        isPwd: false,
      },
      {
        name: 'Veteran (65+)',
        ageAbove: 65,
        gender: 'open' as const,
        isPwd: false,
      },
      { name: 'Beginner', gender: 'open' as const, isPwd: false },
      { name: 'Intermediate', gender: 'open' as const, isPwd: false },
      { name: 'Advanced', gender: 'open' as const, isPwd: false },
      { name: 'PWD', gender: 'open' as const, isPwd: true },
    ];

    const createdCategories = [];
    for (const category of defaultCategories) {
      const categoryId = await ctx.db.insert('categories', {
        ...category,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      createdCategories.push(categoryId);
    }

    return createdCategories;
  },
});
