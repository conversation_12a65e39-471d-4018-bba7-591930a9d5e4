'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  maxLength?: number;
  showCount?: boolean;
  error?: string;
  containerClassName?: string;
  minRows?: number;
  maxRows?: number;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      containerClassName,
      label,
      maxLength,
      showCount = false,
      error,
      minRows = 3,
      maxRows = 8,
      ...props
    },
    ref,
  ) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);
    const mergedRef = useMergedRef(ref, textareaRef);

    // Handle focus state
    const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
      setIsFocused(false);
      props.onBlur?.(e);
    };

    // Get the current value from props or textarea element
    const getCurrentValue = () => {
      if (props.value !== undefined) {
        return props.value;
      }
      return textareaRef.current?.value || props.defaultValue || '';
    };

    // Handle value change
    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      props.onChange?.(e);
    };

    // Auto-resize functionality
    React.useEffect(() => {
      const textarea = textareaRef.current;
      if (textarea) {
        // Reset height to auto to get the correct scrollHeight
        textarea.style.height = 'auto';

        // Calculate the number of rows based on content
        const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
        const padding =
          parseInt(getComputedStyle(textarea).paddingTop) +
          parseInt(getComputedStyle(textarea).paddingBottom);

        const contentHeight = textarea.scrollHeight - padding;
        const rows = Math.ceil(contentHeight / lineHeight);

        // Constrain between minRows and maxRows
        const constrainedRows = Math.max(minRows, Math.min(maxRows, rows));
        const newHeight = constrainedRows * lineHeight + padding;

        textarea.style.height = `${newHeight}px`;
      }
    }, [getCurrentValue(), minRows, maxRows]);

    // Determine if the label should float
    const shouldFloat = isFocused || Boolean(getCurrentValue());

    return (
      <div className={cn('relative w-full', containerClassName)}>
        <div
          className={cn(
            'group relative w-full rounded-md border transition-all duration-200',
            shouldFloat
              ? 'border-primary ring-[3px] ring-primary/20'
              : 'border-input',
            error ? 'border-destructive ring-destructive/20' : '',
            className,
          )}
        >
          <textarea
            className={cn(
              'peer w-full bg-transparent px-4 pb-2 text-base outline-none transition-all resize-none',
              'min-h-[3.5rem] rounded-md',
              'placeholder:text-transparent', // Hide the placeholder when not focused
              'disabled:cursor-not-allowed disabled:opacity-50',
              error ? 'text-destructive-foreground' : 'text-foreground',
              // Adjust top padding based on whether label is floating
              shouldFloat ? 'pt-6' : 'pt-4',
            )}
            ref={mergedRef}
            {...props}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            style={{ overflow: 'hidden' }}
          />
          <label
            className={cn(
              'absolute left-4 text-muted-foreground transition-all duration-200 pointer-events-none z-10',
              shouldFloat ? 'top-2 text-xs' : 'top-4 text-base',
              isFocused && !error ? 'text-primary' : '',
              error ? 'text-destructive' : '',
            )}
          >
            {label}
          </label>

          {showCount && maxLength && (
            <div className="absolute right-4 top-2 text-xs text-muted-foreground z-10">
              {String(getCurrentValue()).length}/{maxLength}
            </div>
          )}
        </div>

        {error && <p className="mt-1 text-xs text-destructive">{error}</p>}
      </div>
    );
  },
);

Textarea.displayName = 'Textarea';

// Helper function to merge refs
function useMergedRef<T>(
  ...refs: Array<React.Ref<T> | undefined>
): React.RefCallback<T> {
  return React.useCallback(
    (value: T) => {
      refs.forEach((ref) => {
        if (typeof ref === 'function') {
          ref(value);
        } else if (ref != null) {
          (ref as React.MutableRefObject<T>).current = value;
        }
      });
    },
    [refs],
  );
}

export { Textarea };
