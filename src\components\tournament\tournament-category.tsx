'use client';

import * as React from 'react';
import { CategorySelector } from './category-selector';
import { PrizeDistribution, type PrizePlace } from './prize-distribution';
import { cn } from '@/lib/utils';

export interface TournamentCategoryData {
  id: string;
  name: string;
  prizes: PrizePlace[];
  isDefault?: boolean;
}

export interface TournamentCategoryProps {
  category: TournamentCategoryData;
  onCategoryChange: (category: TournamentCategoryData) => void;
  className?: string;
}

export function TournamentCategory({
  category,
  onCategoryChange,
  className,
}: TournamentCategoryProps) {
  const handleNameChange = (name: string) => {
    onCategoryChange({
      ...category,
      name,
    });
  };

  const handlePrizesChange = (prizes: PrizePlace[]) => {
    onCategoryChange({
      ...category,
      prizes,
    });
  };

  return (
    <div className={cn('space-y-6 pt-4', className)}>
      {/* Category Selection - Hidden for default category */}
      {!category.isDefault && (
        <CategorySelector
          value={category.name}
          onValueChange={handleNameChange}
          label="Category"
        />
      )}

      {/* Prize Distribution */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">
          Prize Distribution
        </h4>
        <PrizeDistribution
          prizes={category.prizes}
          onPrizesChange={handlePrizesChange}
        />
      </div>
    </div>
  );
}
