'use client';

import { useTournaments } from '@/hooks/use-tournaments';
import { formatDateTime } from '@/lib/tournament-utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Id } from '@convex/_generated/dataModel';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Calendar, MapPin, Users, Trophy, Clock } from 'lucide-react';

export function TournamentList() {
  const { userTournaments } = useTournaments();

  if (!userTournaments) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading tournaments...</p>
        </div>
      </div>
    );
  }

  if (userTournaments.length === 0) {
    return (
      <div className="text-center p-8">
        <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No tournaments yet</h3>
        <p className="text-muted-foreground mb-4">
          Create your first tournament to get started.
        </p>
        <Button asChild>
          <a href="/tournament/create">Create Tournament</a>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">My Tournaments</h2>
        <Button asChild>
          <a href="/tournament/create">Create New Tournament</a>
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {userTournaments.map((tournament) => (
          <TournamentCard key={tournament._id} tournament={tournament} />
        ))}
      </div>
    </div>
  );
}

interface Tournament {
  _id: Id<'tournaments'>;
  name: string;
  format?: string;
  status: string;
  description?: string;
  startDateTime: number;
  location?: string;
  timeControlMinutes: number;
  timeControlIncrement: number;
  limitParticipants?: boolean;
  maxParticipants?: number;
}

function TournamentCard({ tournament }: { tournament: Tournament }) {
  const { updateTournamentStatus, deleteTournament } = useTournaments();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'secondary';
      case 'published':
        return 'default';
      case 'ongoing':
        return 'destructive';
      case 'completed':
        return 'outline';
      case 'cancelled':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const handlePublish = async () => {
    try {
      await updateTournamentStatus({
        tournamentId: tournament._id,
        status: 'published',
      });
    } catch (error) {
      console.error('Error publishing tournament:', error);
    }
  };

  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this tournament?')) {
      try {
        await deleteTournament({ tournamentId: tournament._id });
      } catch (error) {
        console.error('Error deleting tournament:', error);
      }
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="line-clamp-2">{tournament.name}</CardTitle>
            {tournament.format && (
              <CardDescription>{tournament.format}</CardDescription>
            )}
          </div>
          <Badge variant={getStatusColor(tournament.status)}>
            {tournament.status}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="flex-1 space-y-4">
        {tournament.description && (
          <p className="text-sm text-muted-foreground line-clamp-3">
            {tournament.description}
          </p>
        )}

        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span>{formatDateTime(tournament.startDateTime)}</span>
          </div>

          {tournament.location && (
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="line-clamp-1">{tournament.location}</span>
            </div>
          )}

          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span>
              {tournament.timeControlMinutes}+{tournament.timeControlIncrement}
            </span>
          </div>

          {tournament.limitParticipants && tournament.maxParticipants && (
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span>Max {tournament.maxParticipants} players</span>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="flex gap-2">
        {tournament.status === 'draft' && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={handlePublish}
              className="flex-1"
            >
              Publish
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDelete}
              className="text-destructive hover:text-destructive"
            >
              Delete
            </Button>
          </>
        )}

        {tournament.status === 'published' && (
          <Button variant="outline" size="sm" className="flex-1">
            View Details
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}

// Example usage in dashboard page:
/*
// In your dashboard page.tsx:

import { TournamentList } from '@/components/tournament/tournament-list';

export default function DashboardPage() {
  return (
    <div className="container mx-auto py-8">
      <TournamentList />
    </div>
  );
}
*/
