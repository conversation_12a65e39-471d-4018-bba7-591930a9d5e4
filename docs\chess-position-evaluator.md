# Chess Position Evaluator

The Chess Position Evaluator is an AI-powered feature that allows users to get detailed analysis of chess positions using OpenAI's GPT-4 model.

## Features

- **Position Input**: Accept chess positions in FEN notation or natural language descriptions
- **Visual Chess Board**: Interactive chess board that automatically displays when valid FEN notation is detected
- **AI Analysis**: Comprehensive evaluation including:
  - Overall position assessment
  - Key tactical and strategic features
  - Recommended moves with explanations
  - Piece activity and king safety analysis
- **User-Friendly Interface**: Clean form with validation and loading states
- **Board Controls**: Flip board orientation and load starting position
- **Formatted Results**: Easy-to-read analysis with markdown formatting

## Setup

### 1. Environment Variables

Add your OpenAI API key to your `.env.local` file:

```bash
OPENAI_API_KEY=your_openai_api_key_here
```

### 2. API Route

The feature uses a Next.js API route at `/api/evaluate-position` that:

- Accepts POST requests with position data
- Validates input
- Calls OpenAI's GPT-4 model
- Returns formatted analysis

### 3. Page Component

Located at `/evaluate-chess-position`, the page includes:

- Form with textarea for position input
- Validation using zod schema
- Loading states and error handling
- Formatted results display

## Usage

### Input Formats

**FEN Notation (Precise):**

```
rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
```

**Natural Language (Flexible):**

```
White has a queen and king vs black king and rook on the 7th rank
```

```
Sicilian Defense after 1.e4 c5 2.Nf3 d6 3.d4 cxd4 4.Nxd4
```

### Example Analysis Output

The AI provides structured analysis including:

1. **Overall Assessment**: Position evaluation (equal, slight advantage, winning, etc.)
2. **Key Features**: Important pieces, pawn structure, king safety
3. **Tactical Opportunities**: Immediate tactics, threats, combinations
4. **Strategic Considerations**: Long-term plans, weaknesses
5. **Recommended Moves**: Best candidate moves with explanations

## Technical Implementation

### API Route (`/api/evaluate-position/route.ts`)

- Uses OpenAI SDK v5.1.1
- GPT-4 model with chess expert system prompt
- Error handling and validation
- Token usage tracking

### Frontend (`/evaluate-chess-position/page.tsx`)

- React Hook Form with zod validation
- shadcn/ui components
- Loading states and toast notifications
- Markdown-style formatting for results

### Types (`/types/chess.ts`)

- TypeScript interfaces for API requests/responses
- Type safety for form data and API communication

## Dependencies

- `openai`: ^5.1.1 - OpenAI SDK for API calls
- `chessground`: ^9.2.1 - Interactive chess board component
- `react-hook-form`: Form handling and validation
- `zod`: Schema validation
- `sonner`: Toast notifications
- `lucide-react`: Icons

## Error Handling

The feature includes comprehensive error handling for:

- Missing API keys
- Invalid input formats
- OpenAI API errors
- Network issues
- Rate limiting

## Future Enhancements

Potential improvements could include:

- Chess board visualization
- Position history/favorites
- Multiple engine comparisons
- Export analysis to PDF
- Integration with chess databases
