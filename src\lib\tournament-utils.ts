import { type TournamentCategoryData } from '@/components/tournament/tournament-category';
import { type TimeControlValue } from '@/components/common/time-control';

// Convert form data to database format
export interface CreateTournamentData {
  // Basic tournament information
  name: string;
  format?: string;
  description?: string;
  bannerImageId?: string;

  // Time control
  timeControlMinutes: number;
  timeControlIncrement: number;

  // Location
  location?: string;

  // Schedule
  startDateTime: number;
  endDateTime?: number;
  registrationDeadline?: number;

  // Organizer information
  organizerName?: string;
  organizerEmail?: string;
  organizerPhone?: string;
  organizerSocialLinks?: string;

  // Chief arbiter information
  arbiterName?: string;
  arbiterContact?: string;

  // Settings
  limitParticipants: boolean;
  maxParticipants?: number;
  isFideRated: boolean;
  limitByRating: boolean;
  ratingLimit?: number;
  ratingType?: 'local' | 'fide';
  equipment: 'organizer' | 'players' | 'mixed';
  additionalInfo?: string;

  // Categories
  categories: Array<{
    name: string;
    entryFee: number;
    prizes: Array<{
      place: number;
      amount: number;
    }>;
  }>;

  // Sponsors
  sponsors?: Array<{
    name: string;
    logoImageId?: string;
    website?: string;
  }>;
}

// Convert form state to database format
export const convertFormToTournamentData = (formData: {
  // Basic info
  tournamentName: string;
  tournamentFormat: string;
  description: string;
  bannerImageId?: string;

  // Time control
  timeControl: TimeControlValue;

  // Location
  location: string;

  // Schedule
  startDateTime?: Date;
  endDateTime?: Date;
  registrationDeadline?: Date;

  // Organizer
  organizerName: string;
  organizerEmail: string;
  organizerPhone: string;
  organizerSocialLinks: string;

  // Arbiter
  arbiterName: string;
  arbiterContact: string;

  // Settings
  limitParticipants: boolean;
  maxParticipants: string;
  isFideRated: boolean;
  limitByRating: boolean;
  ratingLimit: string;
  ratingType: string;
  equipment: string;
  additionalInfo: string;

  // Categories
  categories: TournamentCategoryData[];

  // Sponsors
  sponsors: Array<{
    id: string;
    name: string;
    logo: File | null;
    logoImageId?: string;
    website: string;
  }>;
}): CreateTournamentData => {
  // Convert time control to numbers
  const timeControlMinutes = parseInt(formData.timeControl.time) || 0;
  const timeControlIncrement = parseInt(formData.timeControl.increment) || 0;

  // Convert categories
  const categories = formData.categories.map((category) => ({
    name: category.name,
    entryFee: 0, // Using global entry fee now, not per-category
    prizes: category.prizes.map((prize) => ({
      place: prize.place,
      amount: parseFloat(prize.amount) * 100 || 0, // Convert to cents
    })),
  }));

  // Convert sponsors
  const sponsors = formData.sponsors
    .filter((sponsor) => sponsor.name.trim() !== '')
    .map((sponsor) => ({
      name: sponsor.name,
      logoImageId: sponsor.logoImageId,
      website: sponsor.website || undefined,
    }));

  return {
    name: formData.tournamentName,
    format: formData.tournamentFormat || undefined,
    description: formData.description || undefined,
    bannerImageId: formData.bannerImageId,
    timeControlMinutes,
    timeControlIncrement,
    location: formData.location || undefined,
    startDateTime: formData.startDateTime?.getTime() || Date.now(),
    endDateTime: formData.endDateTime?.getTime(),
    registrationDeadline: formData.registrationDeadline?.getTime(),
    organizerName: formData.organizerName || undefined,
    organizerEmail: formData.organizerEmail || undefined,
    organizerPhone: formData.organizerPhone || undefined,
    organizerSocialLinks: formData.organizerSocialLinks || undefined,
    arbiterName: formData.arbiterName || undefined,
    arbiterContact: formData.arbiterContact || undefined,
    limitParticipants: formData.limitParticipants,
    maxParticipants: formData.limitParticipants
      ? parseInt(formData.maxParticipants) || undefined
      : undefined,
    isFideRated: formData.isFideRated,
    limitByRating: formData.limitByRating,
    ratingLimit: formData.limitByRating
      ? parseInt(formData.ratingLimit) || undefined
      : undefined,
    ratingType: formData.limitByRating
      ? (formData.ratingType as 'local' | 'fide')
      : undefined,
    equipment: formData.equipment as 'organizer' | 'players' | 'mixed',
    additionalInfo: formData.additionalInfo || undefined,
    categories,
    sponsors: sponsors.length > 0 ? sponsors : undefined,
  };
};

// Convert database format back to display format
export const formatCurrency = (amountInCents: number): string => {
  return `₱${(amountInCents / 100).toLocaleString('en-PH', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })}`;
};

// Convert timestamp to readable date
export const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleDateString('en-PH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// Convert timestamp to readable date and time
export const formatDateTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('en-PH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Validate tournament data before submission
export const validateTournamentData = (
  data: CreateTournamentData,
): string[] => {
  const errors: string[] = [];

  if (!data.name.trim()) {
    errors.push('Tournament name is required');
  }

  if (data.timeControlMinutes <= 0) {
    errors.push('Time control must be greater than 0');
  }

  if (!data.startDateTime || data.startDateTime <= Date.now()) {
    errors.push('Start date must be in the future');
  }

  if (data.endDateTime && data.endDateTime <= data.startDateTime) {
    errors.push('End date must be after start date');
  }

  if (
    data.registrationDeadline &&
    data.registrationDeadline >= data.startDateTime
  ) {
    errors.push('Registration deadline must be before start date');
  }

  if (data.categories.length === 0) {
    errors.push('At least one category is required');
  }

  data.categories.forEach((category, index) => {
    if (!category.name.trim()) {
      errors.push(`Category ${index + 1} name is required`);
    }

    if (category.entryFee < 0) {
      errors.push(`Category ${index + 1} entry fee cannot be negative`);
    }

    if (category.prizes.length === 0) {
      errors.push(`Category ${index + 1} must have at least one prize`);
    }

    category.prizes.forEach((prize, prizeIndex) => {
      if (prize.amount < 0) {
        errors.push(
          `Category ${index + 1}, prize ${
            prizeIndex + 1
          } amount cannot be negative`,
        );
      }
    });
  });

  if (
    data.limitParticipants &&
    (!data.maxParticipants || data.maxParticipants <= 0)
  ) {
    errors.push(
      'Maximum participants must be greater than 0 when limiting participants',
    );
  }

  if (data.limitByRating && (!data.ratingLimit || data.ratingLimit <= 0)) {
    errors.push('Rating limit must be greater than 0 when limiting by rating');
  }

  return errors;
};
