'use client';

import * as React from 'react';
import { Chessground } from 'chessground';
import type { Api } from 'chessground/api';
import type { Config } from 'chessground/config';
import { cn } from '@/lib/utils';
import type { ChessBoardProps } from '@/types/chess';

// Import chessground CSS
import 'chessground/assets/chessground.base.css';
import 'chessground/assets/chessground.brown.css';
import 'chessground/assets/chessground.cburnett.css';

const ChessBoard: React.FC<ChessBoardProps> = ({
  fen,
  orientation = 'white',
  viewOnly = true,
  className,
  onMove,
}) => {
  const boardRef = React.useRef<HTMLDivElement>(null);
  const chessgroundRef = React.useRef<Api | null>(null);

  // Initialize chessground once
  React.useEffect(() => {
    if (!boardRef.current) return;

    // Initialize chessground with basic config
    const config: Config = {
      orientation: 'white', // Will be updated in separate effect
      viewOnly: true, // Will be updated in separate effect
      coordinates: true,
      highlight: {
        lastMove: true,
        check: true,
      },
      animation: {
        enabled: true,
        duration: 200,
      },
      movable: {
        free: false,
        color: undefined,
        dests: new Map(),
      },
      drawable: {
        enabled: false,
      },
    };

    chessgroundRef.current = Chessground(boardRef.current, config);

    return () => {
      if (chessgroundRef.current) {
        chessgroundRef.current.destroy();
      }
    };
  }, []);

  // Update position when FEN changes
  React.useEffect(() => {
    if (chessgroundRef.current) {
      if (fen) {
        chessgroundRef.current.set({
          fen,
          lastMove: undefined,
        });
      } else {
        // Set default starting position if no FEN provided
        chessgroundRef.current.set({
          fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
          lastMove: undefined,
        });
      }
    }
  }, [fen]);

  // Update orientation when it changes
  React.useEffect(() => {
    if (chessgroundRef.current) {
      chessgroundRef.current.set({ orientation });
    }
  }, [orientation]);

  // Update viewOnly and movable settings when they change
  React.useEffect(() => {
    if (chessgroundRef.current) {
      const movableConfig: Config['movable'] = {
        free: false,
        color: viewOnly ? undefined : 'both',
        dests: new Map(),
      };

      // Add move handler if provided and not view only
      if (onMove && !viewOnly) {
        movableConfig.events = {
          after: (orig, dest) => {
            onMove(orig, dest);
          },
        };
      }

      chessgroundRef.current.set({
        viewOnly,
        movable: movableConfig,
      });
    }
  }, [viewOnly, onMove]);

  return (
    <div
      ref={boardRef}
      className={cn(
        'chess-board',
        'w-full max-w-md mx-auto',
        'aspect-square',
        'border border-border rounded-lg overflow-hidden',
        'shadow-sm',
        className,
      )}
      style={{
        // Ensure the board is properly sized
        minHeight: '300px',
        maxHeight: '500px',
      }}
    />
  );
};

export default ChessBoard;
