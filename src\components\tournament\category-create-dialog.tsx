'use client';

import * as React from 'react';
import { useMutation } from 'convex/react';
import { api } from '@convex/_generated/api';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/common/input';
import { Combobox } from '@/components/common/combobox';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Users, Calendar } from 'lucide-react';
import { toast } from 'sonner';

interface CategoryCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCategoryCreated: (categoryName: string) => void;
}

interface CategoryFormData {
  name: string;
  ageUnder: string;
  ageAbove: string;
  gender: string;
  isPwd: boolean;
}

const genderOptions = [
  { value: 'open', label: 'Open' },
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
];

export function CategoryCreateDialog({
  open,
  onOpenChange,
  onCategoryCreated,
}: CategoryCreateDialogProps) {
  const [formData, setFormData] = React.useState<CategoryFormData>({
    name: '',
    ageUnder: '',
    ageAbove: '',
    gender: 'open',
    isPwd: false,
  });
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [errors, setErrors] = React.useState<Partial<CategoryFormData>>({});

  const createCategory = useMutation(api.categories.createCategory);

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setFormData({
        name: '',
        ageUnder: '',
        ageAbove: '',
        gender: 'open',
        isPwd: false,
      });
      setErrors({});
      setIsSubmitting(false);
    }
  }, [open]);

  const validateForm = (): boolean => {
    const newErrors: Partial<CategoryFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Category name is required';
    }

    if (formData.ageUnder && isNaN(Number(formData.ageUnder))) {
      newErrors.ageUnder = 'Age must be a valid number';
    }

    if (formData.ageAbove && isNaN(Number(formData.ageAbove))) {
      newErrors.ageAbove = 'Age must be a valid number';
    }

    if (
      formData.ageUnder &&
      formData.ageAbove &&
      Number(formData.ageUnder) <= Number(formData.ageAbove)
    ) {
      newErrors.ageUnder = 'Age under must be greater than age above';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await createCategory({
        name: formData.name.trim(),
        ageUnder: formData.ageUnder ? Number(formData.ageUnder) : undefined,
        ageAbove: formData.ageAbove ? Number(formData.ageAbove) : undefined,
        gender: formData.gender as 'male' | 'female' | 'open',
        isPwd: formData.isPwd,
      });

      toast.success('Category created successfully');
      onCategoryCreated(formData.name.trim());
    } catch (error) {
      console.error('Error creating category:', error);
      toast.error('Failed to create category. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateFormData = (
    field: keyof CategoryFormData,
    value: string | boolean,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Category</DialogTitle>
          <DialogDescription>
            Define a new tournament category with specific criteria.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Category Name"
            value={formData.name}
            onChange={(e) => updateFormData('name', e.target.value)}
            error={errors.name}
            placeholder="e.g., Under 16, Women's Open"
            required
          />

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Age Under"
              type="number"
              value={formData.ageUnder}
              onChange={(e) => updateFormData('ageUnder', e.target.value)}
              error={errors.ageUnder}
              placeholder="e.g., 16"
              min="1"
              max="100"
              icon={<Calendar className="h-4 w-4" />}
            />

            <Input
              label="Age Above"
              type="number"
              value={formData.ageAbove}
              onChange={(e) => updateFormData('ageAbove', e.target.value)}
              error={errors.ageAbove}
              placeholder="e.g., 50"
              min="1"
              max="100"
              icon={<Calendar className="h-4 w-4" />}
            />
          </div>

          <Combobox
            value={formData.gender}
            onValueChange={(value) => updateFormData('gender', value)}
            options={genderOptions}
            label="Gender"
            searchable={false}
            icon={<Users className="h-4 w-4" />}
            iconPosition="right"
          />

          <div className="flex items-center space-x-2">
            <Switch
              id="pwd-switch"
              checked={formData.isPwd}
              onCheckedChange={(checked) => updateFormData('isPwd', checked)}
            />
            <Label htmlFor="pwd-switch" className="text-sm font-medium">
              People with Disability (PWD) Category
            </Label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Category'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
