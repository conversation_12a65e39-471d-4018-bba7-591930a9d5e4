'use client';

import { useParams } from 'next/navigation';
import { Id } from '@convex/_generated/dataModel';
import CreateTournament from '@/app/(auth)/tournament/create/page';

const EditTournament = () => {
  const params = useParams();
  const tournamentId = params.id as Id<'tournaments'>;

  // Reuse the CreateTournament component but pass the tournament ID
  // This will load the existing tournament data
  return <CreateTournament tournamentId={tournamentId} />;
};

export default EditTournament;
