'use client';

import { DateTimePicker } from '@/components/common/date-time-picker';
import { Input } from '@/components/common/input';
import { LocationField } from '@/components/common/location-field';
import { Textarea } from '@/components/common/textarea';
import { TimeControl } from '@/components/common/time-control';
import { Header } from '@/components/header';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { CategoryManager } from '@/components/tournament/category-manager';
import { useTournamentForm } from '@/hooks/use-tournament-form';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Id } from '@convex/_generated/dataModel';
import { Plus, Trophy, Calendar, DollarSign, Tag } from 'lucide-react';

interface TournamentFormProps {
  tournamentId?: Id<'tournaments'>;
}

export function TournamentForm({ tournamentId }: TournamentFormProps) {
  const router = useRouter();

  // Use the tournament form hook for state management
  const {
    formState,
    setFormState,
    currentTournamentId,
    saveField,
    saveSchedule,
    saveTimeControl,
    saveLocation,
  } = useTournamentForm(tournamentId);

  // Redirect to edit page after tournament is created
  useEffect(() => {
    if (
      currentTournamentId &&
      !tournamentId && // Only redirect if we're not already editing a tournament
      window.location.pathname === '/tournament/create'
    ) {
      router.replace(`/tournament/edit/${currentTournamentId}`);
    }
  }, [currentTournamentId, tournamentId, router]);

  // Helper function to update form state
  const updateFormState = (updates: Partial<typeof formState>) => {
    setFormState((prev) => ({ ...prev, ...updates }));
  };

  // Helper function to sync default category name with tournament name
  const syncDefaultCategoryName = (tournamentName: string) => {
    setFormState((prev) => ({
      ...prev,
      categories: prev.categories.map((category) =>
        category.isDefault ? { ...category, name: tournamentName } : category,
      ),
    }));
  };

  return (
    <>
      <Header />
      <main className="container mx-auto max-w-5xl py-8">
        <h1 className="text-3xl font-bold mb-4">
          {tournamentId ? 'Edit Tournament' : 'Create a new tournament'}
        </h1>
        <div className="space-y-4">
          <Input
            label="Tournament Name"
            value={formState.tournamentName}
            onChange={(e) => {
              const newName = e.target.value;
              updateFormState({ tournamentName: newName });
              syncDefaultCategoryName(newName);
            }}
            onBlur={() => saveField('basic')}
          />

          <Input
            label="Tournament Format"
            value={formState.tournamentFormat}
            onChange={(e) =>
              updateFormState({ tournamentFormat: e.target.value })
            }
            onBlur={() => saveField('basic')}
          />

          <TimeControl
            value={formState.timeControl}
            onValueChange={(timeControl) => {
              updateFormState({ timeControl });
              saveTimeControl(timeControl);
            }}
          />

          <LocationField
            label="Add Location"
            value={formState.location}
            onChange={(location) => updateFormState({ location })}
            onLocationSelect={(locationData) => {
              if (locationData.placeId && locationData.placeId !== 'fallback') {
                saveLocation({
                  formattedAddress: locationData.address,
                  lat: locationData.coordinates.lat,
                  lng: locationData.coordinates.lng,
                  placeId: locationData.placeId,
                });
              }
            }}
          />

          <Textarea
            label="About the Tournament"
            value={formState.description}
            onChange={(e) => updateFormState({ description: e.target.value })}
            onBlur={() => saveField('basic')}
            maxLength={500}
            showCount={true}
            minRows={3}
            maxRows={6}
          />

          <Accordion type="single" collapsible className="w-full space-y-4">
            <AccordionItem
              value="schedules"
              className="border border-border rounded-lg bg-background shadow-sm"
            >
              <AccordionTrigger className="cursor-pointer px-4 py-4 hover:no-underline hover:bg-muted/50 rounded-lg [&[data-state=open]]:rounded-b-none [&[data-state=open]]:border-b [&[data-state=open]]:border-border">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium text-base">Schedules</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-6">
                  {/* Micro copy */}
                  <div className="space-y-1 pt-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Tournament Schedule
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Set the dates and times for your tournament
                    </p>
                  </div>

                  <div className="space-y-4">
                    <DateTimePicker
                      value={formState.startDateTime}
                      onChange={(startDateTime) => {
                        updateFormState({ startDateTime });
                        saveSchedule({
                          startDateTime,
                          endDateTime: formState.endDateTime,
                          registrationDeadline: formState.registrationDeadline,
                        });
                      }}
                      dateLabel="Start Date"
                      timeLabel="Start Time"
                      timeStep={15}
                      disablePastDates={true}
                    />

                    {!formState.showEndDateTime && (
                      <button
                        type="button"
                        onClick={() =>
                          updateFormState({ showEndDateTime: true })
                        }
                        className="flex items-center gap-1 text-primary hover:text-primary/95 text-sm font-medium transition-colors cursor-pointer"
                      >
                        <Plus className="h-4 w-4" />
                        End date and time
                      </button>
                    )}

                    {formState.showEndDateTime && (
                      <DateTimePicker
                        value={formState.endDateTime}
                        onChange={(endDateTime) => {
                          updateFormState({ endDateTime });
                          saveSchedule({
                            startDateTime: formState.startDateTime,
                            endDateTime,
                            registrationDeadline:
                              formState.registrationDeadline,
                          });
                        }}
                        dateLabel="End Date"
                        timeLabel="End Time"
                        timeStep={15}
                        disablePastDates={true}
                      />
                    )}

                    <DateTimePicker
                      value={formState.registrationDeadline}
                      onChange={(registrationDeadline) => {
                        updateFormState({ registrationDeadline });
                        saveSchedule({
                          startDateTime: formState.startDateTime,
                          endDateTime: formState.endDateTime,
                          registrationDeadline,
                        });
                      }}
                      dateLabel="Registration Deadline"
                      timeLabel="Deadline Time"
                      timeStep={15}
                      disablePastDates={true}
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem
              value="price-structure"
              className="border border-border rounded-lg bg-background shadow-sm"
            >
              <AccordionTrigger className="cursor-pointer px-4 py-4 hover:no-underline hover:bg-muted/50 rounded-lg [&[data-state=open]]:rounded-b-none [&[data-state=open]]:border-b [&[data-state=open]]:border-border">
                <div className="flex items-center gap-3">
                  <Trophy className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium text-base">Price Structure</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-6">
                  {/* Micro copy */}
                  <div className="space-y-1 pt-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Prize Distribution
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Define tournament categories and prize distribution
                    </p>
                  </div>

                  <CategoryManager
                    categories={formState.categories}
                    onCategoriesChange={(categories) =>
                      updateFormState({ categories })
                    }
                    tournamentName={formState.tournamentName}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem
              value="entry-fee-promotions"
              className="border border-border rounded-lg bg-background shadow-sm"
            >
              <AccordionTrigger className="cursor-pointer px-4 py-4 hover:no-underline hover:bg-muted/50 rounded-lg [&[data-state=open]]:rounded-b-none [&[data-state=open]]:border-b [&[data-state=open]]:border-border">
                <div className="flex items-center gap-3">
                  <DollarSign className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium text-base">
                    Entry Fee & Promotions
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="space-y-6">
                  {/* Micro copy */}
                  <div className="space-y-1 pt-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      Tournament Entry Fee
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Set the entry fee for your tournament and add promotional
                      codes
                    </p>
                  </div>

                  <div className="space-y-4">
                    {/* Entry Fee Field */}
                    <Input
                      label="Entry Fee (₱)"
                      type="number"
                      value={formState.entryFee || ''}
                      onChange={(e) =>
                        updateFormState({ entryFee: e.target.value })
                      }
                      onBlur={() => saveField('basic')}
                      placeholder="0"
                      min="0"
                      step="1"
                      icon={<DollarSign className="h-4 w-4" />}
                    />

                    {/* Coupon/Promotion Section */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-foreground">
                          Promotional Codes
                        </h4>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // TODO: Implement add coupon functionality
                            console.log('Add coupon clicked');
                          }}
                        >
                          <Tag className="h-4 w-4 mr-2" />
                          Add Coupon
                        </Button>
                      </div>

                      {/* Placeholder for coupon list */}
                      <div className="text-center py-6 text-muted-foreground border border-dashed border-border rounded-lg">
                        <Tag className="h-6 w-6 mx-auto mb-2 opacity-50" />
                        <p className="text-sm font-medium">
                          No promotional codes yet
                        </p>
                        <p className="text-xs mt-1">
                          Add coupon codes to offer discounts to participants
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </main>
    </>
  );
}
