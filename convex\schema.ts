import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  // Users table for storing user information
  users: defineTable({
    clerkId: v.string(),
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    imageUrl: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index('by_clerk_id', ['clerkId'])
    .index('by_email', ['email']),

  // Locations table for storing venue/address information
  locations: defineTable({
    formattedAddress: v.string(),
    lat: v.number(),
    lng: v.number(),
    placeId: v.string(), // Google Places ID
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index('by_place_id', ['placeId'])
    .index('by_coordinates', ['lat', 'lng']),

  // Main tournaments table
  tournaments: defineTable({
    // Basic tournament information
    name: v.string(),
    format: v.optional(v.string()),
    description: v.optional(v.string()),
    bannerImageId: v.optional(v.id('_storage')),

    // Time control
    timeControlMinutes: v.number(),
    timeControlIncrement: v.number(),

    // Location
    locationId: v.optional(v.id('locations')),

    // Schedule
    startDateTime: v.number(),
    endDateTime: v.optional(v.number()),
    registrationDeadline: v.optional(v.number()),

    // Organizer information
    organizerName: v.optional(v.string()),
    organizerEmail: v.optional(v.string()),
    organizerPhone: v.optional(v.string()),
    organizerSocialLinks: v.optional(v.string()),

    // Chief arbiter information
    arbiterName: v.optional(v.string()),
    arbiterContact: v.optional(v.string()),

    // Settings
    limitParticipants: v.boolean(),
    maxParticipants: v.optional(v.number()),
    isFideRated: v.boolean(),
    limitByRating: v.boolean(),
    ratingLimit: v.optional(v.number()),
    ratingType: v.optional(v.union(v.literal('local'), v.literal('fide'))),
    equipment: v.union(
      v.literal('organizer'),
      v.literal('players'),
      v.literal('mixed'),
    ),
    additionalInfo: v.optional(v.string()),

    // Entry fee (global tournament entry fee)
    entryFee: v.optional(v.number()), // Store as number in cents/smallest currency unit

    // Status and metadata
    status: v.union(
      v.literal('draft'),
      v.literal('published'),
      v.literal('ongoing'),
      v.literal('completed'),
      v.literal('cancelled'),
    ),
    createdBy: v.id('users'),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index('by_creator', ['createdBy'])
    .index('by_status', ['status'])
    .index('by_start_date', ['startDateTime'])
    .index('by_created_at', ['createdAt'])
    .index('by_location', ['locationId']),

  // Categories table for tournament category definitions
  categories: defineTable({
    name: v.string(),
    ageUnder: v.optional(v.number()),
    ageAbove: v.optional(v.number()),
    gender: v.optional(
      v.union(v.literal('male'), v.literal('female'), v.literal('open')),
    ),
    isPwd: v.boolean(),

    // Metadata
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index('by_name', ['name'])
    .index('by_gender', ['gender'])
    .index('by_pwd', ['isPwd']),

  // Tournament categories with prizes (links tournaments to categories)
  tournament_categories: defineTable({
    tournamentId: v.id('tournaments'),
    categoryId: v.id('categories'),

    // Prize distribution as an array of objects
    prizes: v.array(
      v.object({
        place: v.number(),
        amount: v.number(), // Store as number in cents/smallest currency unit
      }),
    ),

    // Metadata
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index('by_tournament', ['tournamentId'])
    .index('by_category', ['categoryId']),

  // Tournament sponsors
  tournament_sponsors: defineTable({
    tournamentId: v.id('tournaments'),
    name: v.string(),
    logoImageId: v.optional(v.id('_storage')),
    website: v.optional(v.string()),

    // Metadata
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index('by_tournament', ['tournamentId']),

  // Tournament participants (for future use)
  tournament_participants: defineTable({
    tournamentId: v.id('tournaments'),
    categoryId: v.id('categories'),
    userId: v.id('users'),

    // Player information
    playerName: v.string(),
    rating: v.optional(v.number()),
    ratingType: v.optional(v.union(v.literal('local'), v.literal('fide'))),

    // Registration details
    registeredAt: v.number(),
    paymentStatus: v.union(
      v.literal('pending'),
      v.literal('paid'),
      v.literal('refunded'),
    ),

    // Status
    status: v.union(
      v.literal('registered'),
      v.literal('confirmed'),
      v.literal('withdrawn'),
      v.literal('disqualified'),
    ),

    // Metadata
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index('by_tournament', ['tournamentId'])
    .index('by_category', ['categoryId'])
    .index('by_user', ['userId'])
    .index('by_tournament_user', ['tournamentId', 'userId']),
});
