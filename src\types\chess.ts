export interface EvaluatePositionRequest {
  position: string;
}

export interface EvaluatePositionResponse {
  evaluation: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface EvaluatePositionError {
  error: string;
}

// Chess board related types
export interface ChessBoardProps {
  fen?: string;
  orientation?: 'white' | 'black';
  viewOnly?: boolean;
  className?: string;
  onMove?: (from: string, to: string) => void;
}

export interface ChessPosition {
  fen: string;
  isValid: boolean;
}
