import { validateFEN, isFEN<PERSON>ike, parseChessPosition, STARTING_POSITION_FEN } from '../chess-utils';

describe('Chess Utils', () => {
  describe('validateFEN', () => {
    it('should validate correct starting position FEN', () => {
      expect(validateFEN(STARTING_POSITION_FEN)).toBe(true);
    });

    it('should validate other correct FEN strings', () => {
      // After 1.e4
      expect(validateFEN('rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1')).toBe(true);
      
      // Sicilian Defense
      expect(validateFEN('rnbqkbnr/pp1ppppp/8/2p5/4P3/8/PPPP1PPP/RNBQKBNR w KQkq c6 0 2')).toBe(true);
    });

    it('should reject invalid FEN strings', () => {
      expect(validateFEN('')).toBe(false);
      expect(validateFEN('invalid')).toBe(false);
      expect(validateFEN('rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP')).toBe(false); // Missing parts
      expect(validateFEN('rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0')).toBe(false); // Missing fullmove
    });
  });

  describe('isFENLike', () => {
    it('should detect FEN-like strings', () => {
      expect(isFENLike(STARTING_POSITION_FEN)).toBe(true);
      expect(isFENLike('rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR')).toBe(true);
    });

    it('should reject non-FEN-like strings', () => {
      expect(isFENLike('This is just text')).toBe(false);
      expect(isFENLike('White has a queen and king')).toBe(false);
      expect(isFENLike('')).toBe(false);
    });
  });

  describe('parseChessPosition', () => {
    it('should parse valid FEN strings', () => {
      const result = parseChessPosition(STARTING_POSITION_FEN);
      expect(result.isValid).toBe(true);
      expect(result.fen).toBe(STARTING_POSITION_FEN);
    });

    it('should handle invalid input', () => {
      const result = parseChessPosition('This is not a FEN string');
      expect(result.isValid).toBe(false);
      expect(result.fen).toBe('');
    });

    it('should extract FEN from mixed text', () => {
      const mixedText = `Here is a position: ${STARTING_POSITION_FEN} - this is the starting position`;
      const result = parseChessPosition(mixedText);
      expect(result.isValid).toBe(true);
      expect(result.fen).toBe(STARTING_POSITION_FEN);
    });
  });
});
