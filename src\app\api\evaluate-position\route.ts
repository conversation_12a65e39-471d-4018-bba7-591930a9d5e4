import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const SYSTEM_PROMPT = `You are a chess grandmaster and expert chess position evaluator. Evaluate the given chess position and provide a comprehensive evaluation. Your goal is to help understand the given position. Your response should include:

1. **Overall Assessment**: Give the right assessment using material balance, king safety, piece activity and pawn structure
2. **Key Features**: Who is better and by how much (slight advantage, significant advantage, winning, etc.), give the evaluation from the engine
3. **Tactical Opportunities**: Any immediate tactics, threats, or combinations
4. **Strategic Considerations**: Long-term plans, weaknesses to exploit
5. **Recommended Plans**: Share recommended plan of activity for white and black so the user can understand the position better

Format your response in clear sections with markdown formatting for readability.`;

export async function POST(request: NextRequest) {
  try {
    const { position } = await request.json();

    if (!position || typeof position !== 'string') {
      return NextResponse.json(
        { error: 'Position is required and must be a string' },
        { status: 400 },
      );
    }

    if (!process.env.OPENAI_API_KEY) {
      return NextResponse.json(
        { error: 'OpenAI API key not configured' },
        { status: 500 },
      );
    }

    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: SYSTEM_PROMPT,
        },
        {
          role: 'user',
          content: `Give me the right assessment of the position: ${position} What would be the right plan for white and black? and how piecess will be deployed`,
        },
      ],
      max_tokens: 1000,
      temperature: 0.7,
    });

    const evaluation = completion.choices[0]?.message?.content;

    if (!evaluation) {
      return NextResponse.json(
        { error: 'Failed to get evaluation from OpenAI' },
        { status: 500 },
      );
    }

    return NextResponse.json({
      evaluation,
      usage: completion.usage,
    });
  } catch (error) {
    console.error('Error evaluating position:', error);

    if (error instanceof Error) {
      return NextResponse.json(
        { error: `Failed to evaluate position: ${error.message}` },
        { status: 500 },
      );
    }

    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 },
    );
  }
}
