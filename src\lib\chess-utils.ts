import type { ChessPosition } from '@/types/chess';

/**
 * Validates if a string is a valid FEN notation
 * FEN format: "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
 */
export function validateFEN(fen: string): boolean {
  if (!fen || typeof fen !== 'string') {
    return false;
  }

  const parts = fen.trim().split(/\s+/);

  // FEN must have 6 parts
  if (parts.length !== 6) {
    return false;
  }

  const [position, activeColor, castling, enPassant, halfmove, fullmove] =
    parts;

  // Validate position part
  const ranks = position.split('/');
  if (ranks.length !== 8) {
    return false;
  }

  for (const rank of ranks) {
    let squares = 0;
    for (const char of rank) {
      if (/[1-8]/.test(char)) {
        squares += parseInt(char, 10);
      } else if (/[prnbqkPRNBQK]/.test(char)) {
        squares += 1;
      } else {
        return false;
      }
    }
    if (squares !== 8) {
      return false;
    }
  }

  // Validate active color
  if (!/^[wb]$/.test(activeColor)) {
    return false;
  }

  // Validate castling rights
  if (!/^(-|[KQkq]+)$/.test(castling)) {
    return false;
  }

  // Validate en passant
  if (!/^(-|[a-h][36])$/.test(enPassant)) {
    return false;
  }

  // Validate halfmove clock
  if (!/^\d+$/.test(halfmove)) {
    return false;
  }

  // Validate fullmove number
  if (!/^[1-9]\d*$/.test(fullmove)) {
    return false;
  }

  return true;
}

/**
 * Checks if a string looks like it might be FEN notation
 * This is a more lenient check for partial FEN strings
 */
export function isFENLike(input: string): boolean {
  if (!input || typeof input !== 'string') {
    return false;
  }

  const trimmed = input.trim();

  // Check if it contains typical FEN characters and structure
  const hasSlashes = (trimmed.match(/\//g) || []).length >= 7;
  const hasChessPieces = /[prnbqkPRNBQK]/.test(trimmed);
  const hasNumbers = /[1-8]/.test(trimmed);

  return hasSlashes && hasChessPieces && hasNumbers;
}

/**
 * Parses input and returns chess position information
 */
export function parseChessPosition(input: string): ChessPosition {
  const trimmed = input.trim();

  if (validateFEN(trimmed)) {
    return {
      fen: trimmed,
      isValid: true,
    };
  }

  // Try to extract FEN from the input if it's mixed with other text
  const fenPattern =
    /([prnbqkPRNBQK1-8\/]+\s+[wb]\s+[KQkq-]+\s+[a-h][36-]\s+\d+\s+\d+)/;
  const match = trimmed.match(fenPattern);

  if (match && validateFEN(match[1])) {
    return {
      fen: match[1],
      isValid: true,
    };
  }

  return {
    fen: '',
    isValid: false,
  };
}

/**
 * Default starting position FEN
 */
export const STARTING_POSITION_FEN =
  'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1';
