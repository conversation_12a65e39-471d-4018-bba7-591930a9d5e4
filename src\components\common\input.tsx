'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  maxLength?: number;
  showCount?: boolean;
  error?: string;
  containerClassName?: string;
  icon?: React.ReactNode;
  rightAction?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      containerClassName,
      label,
      maxLength,
      showCount = false,
      error,
      type = 'text',
      icon,
      rightAction,
      ...props
    },
    ref,
  ) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const inputRef = React.useRef<HTMLInputElement>(null);
    const mergedRef = useMergedRef(ref, inputRef);

    // Handle focus state
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      props.onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      props.onBlur?.(e);
    };

    // Get the current value from props or input element
    const getCurrentValue = () => {
      if (props.value !== undefined) {
        return props.value;
      }
      return inputRef.current?.value || props.defaultValue || '';
    };

    // Handle value change
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      props.onChange?.(e);
    };

    // Determine if the label should float
    const shouldFloat = isFocused || Boolean(getCurrentValue());

    return (
      <div className={cn('relative w-full', containerClassName)}>
        <div
          className={cn(
            'group relative w-full rounded-md border transition-all duration-200',
            shouldFloat
              ? 'border-primary ring-[3px] ring-primary/20'
              : 'border-input',
            error ? 'border-destructive ring-destructive/20' : '',
            className,
          )}
        >
          {icon && (
            <div className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground">
              {icon}
            </div>
          )}
          <input
            type={type}
            className={cn(
              'peer w-full bg-transparent pb-2 pt-4 text-base outline-none transition-all',
              'h-14 rounded-md',
              'placeholder:text-transparent', // Hide the placeholder when not focused
              'disabled:cursor-not-allowed disabled:opacity-50',
              error ? 'text-destructive-foreground' : 'text-foreground',
              icon && rightAction
                ? 'pl-10 pr-10'
                : icon
                ? 'pl-10 pr-4'
                : rightAction
                ? 'pl-4 pr-10'
                : 'px-4', // Adjust padding based on icon and rightAction presence
              // Hide number input spinners
              type === 'number'
                ? '[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
                : '',
            )}
            ref={mergedRef}
            {...props}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
          />
          <label
            className={cn(
              'absolute text-muted-foreground transition-all duration-200',
              icon ? 'left-10' : 'left-4', // Adjust label position based on icon presence
              shouldFloat
                ? 'top-1 text-xs'
                : 'top-1/2 -translate-y-1/2 text-base',
              isFocused && !error ? 'text-primary' : '',
              error ? 'text-destructive' : '',
            )}
            onClick={() => inputRef.current?.focus()}
          >
            {label}
          </label>

          {rightAction && (
            <div className="absolute right-4 top-1/2 -translate-y-1/2 text-muted-foreground">
              {rightAction}
            </div>
          )}

          {showCount && maxLength && (
            <div
              className={cn(
                'absolute top-1 text-xs text-muted-foreground',
                rightAction ? 'right-10' : 'right-4',
              )}
            >
              {String(getCurrentValue()).length}/{maxLength}
            </div>
          )}
        </div>

        {error && <p className="mt-1 text-xs text-destructive">{error}</p>}
      </div>
    );
  },
);

Input.displayName = 'Input';

// Helper function to merge refs
function useMergedRef<T>(
  ...refs: Array<React.Ref<T> | undefined>
): React.RefCallback<T> {
  return React.useCallback(
    (value: T) => {
      refs.forEach((ref) => {
        if (typeof ref === 'function') {
          ref(value);
        } else if (ref != null) {
          (ref as React.RefObject<T>).current = value;
        }
      });
    },
    [refs],
  );
}

export { Input };
